<?php get_header(); ?>

<main class="category-page">
  <div class="container">

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <a href="<?php echo esc_url(home_url('/')); ?>">Início</a>
      <span class="breadcrumb-separator">></span>
      <span class="breadcrumb-current"><?php single_cat_title(); ?></span>
    </nav>

    <!-- Category Header -->
    <div class="category-header">
      <h1 class="category-title"><?php single_cat_title(); ?></h1>
      <?php if (category_description()) : ?>
        <div class="category-description">
          <?php echo category_description(); ?>
        </div>
      <?php endif; ?>
    </div>

    <!-- Posts Grid -->
    <div class="category-content">
      <?php if (have_posts()) : ?>

        <?php
        $post_count = 0;
        $featured_post_shown = false;
        $grid_posts_shown = 0;
        $two_column_layout_started = false;
        ?>

        <!-- Posts 1-5: Featured + Grid Layout -->
        <?php while (have_posts()) : the_post(); ?>
          <?php $post_count++; ?>

          <?php if ($post_count === 1 && !$featured_post_shown) : ?>
            <!-- Featured Post (First Post) -->
            <div class="featured-post-container">
              <?php get_template_part('template-parts/featured-post-card'); ?>
            </div>
            <?php $featured_post_shown = true; ?>

            <!-- Regular Posts Grid (Posts 2-5) -->
            <div class="posts-grid">

          <?php elseif ($post_count >= 2 && $post_count <= 5) : ?>
            <?php get_template_part('template-parts/post-card'); ?>
            <?php $grid_posts_shown++; ?>

            <?php if ($post_count === 5 || !have_posts()) : ?>
              </div> <!-- Close posts-grid -->
            <?php endif; ?>

          <?php elseif ($post_count === 6 && !$two_column_layout_started) : ?>
            <!-- Start Two-Column Layout for Posts 6+ -->
            <div class="category-two-column-layout">
              <div class="category-main-content">
                <div class="see-also-list">
                  <?php get_template_part('template-parts/post-list-item'); ?>
                  <?php $two_column_layout_started = true; ?>

          <?php elseif ($post_count > 6) : ?>
            <?php get_template_part('template-parts/post-list-item'); ?>

          <?php endif; ?>

        <?php endwhile; ?>

        <?php if ($two_column_layout_started) : ?>
                </div> <!-- Close see-also-list -->
              </div> <!-- Close category-main-content -->

              <!-- Sidebar for Two-Column Layout -->
              <div class="category-sidebar">
                <div class="sidebar-content">

                  <!-- Popular Posts Widget -->
                  <div class="popular-posts-widget">
                    <h3 class="widget-title">Mais Lidas</h3>
                    <div class="popular-posts-list">
                      <?php
                      $popular_posts = new WP_Query(array(
                        'posts_per_page' => 5,
                        'meta_key' => 'post_views_count',
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC',
                        'post__not_in' => array(get_the_ID())
                      ));

                      if ($popular_posts->have_posts()) :
                        $popular_count = 0;
                        while ($popular_posts->have_posts()) : $popular_posts->the_post();
                          $popular_count++;
                      ?>
                        <article class="popular-post-item">
                          <span class="popular-post-number"><?php echo $popular_count; ?></span>
                          <div class="popular-post-content">
                            <h4 class="popular-post-title">
                              <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h4>
                            <time class="popular-post-date"><?php echo get_the_date('d/m/Y'); ?></time>
                          </div>
                        </article>
                      <?php
                        endwhile;
                        wp_reset_postdata();
                      endif;
                      ?>
                    </div>
                  </div>

                </div>
              </div> <!-- Close category-sidebar -->
            </div> <!-- Close category-two-column-layout -->
        <?php endif; ?>

        <!-- Pagination -->
        <div class="category-pagination">
          <?php
          $pagination_args = array(
            'mid_size' => 2,
            'prev_text' => '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" fill="currentColor"/></svg>',
            'next_text' => '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z" fill="currentColor"/></svg>',
            'type' => 'array'
          );

          $pagination_links = paginate_links($pagination_args);

          if ($pagination_links) :
          ?>
            <nav class="pagination-nav" aria-label="Navegação de páginas">
              <ul class="pagination-list">
                <?php foreach ($pagination_links as $link) : ?>
                  <li class="pagination-item">
                    <?php echo $link; ?>
                  </li>
                <?php endforeach; ?>
              </ul>
            </nav>
          <?php endif; ?>
        </div>

      <?php else : ?>
        <!-- No Posts Found -->
        <div class="no-posts-found">
          <h2>Nenhum post encontrado</h2>
          <p>Não há posts nesta categoria no momento.</p>
          <a href="<?php echo esc_url(home_url('/')); ?>" class="btn-back-home">Voltar ao início</a>
        </div>
      <?php endif; ?>
    </div>

  </div>
</main>

<?php get_footer(); ?>
