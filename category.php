<?php get_header(); ?>

<main class="category-page">
  <div class="container">

    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav">
      <a href="<?php echo esc_url(home_url('/')); ?>">Início</a>
      <span class="breadcrumb-separator">></span>
      <span class="breadcrumb-current"><?php single_cat_title(); ?></span>
    </nav>

    <!-- Category Header -->
    <div class="category-header">
      <h1 class="category-title"><?php single_cat_title(); ?></h1>
      <?php if (category_description()) : ?>
        <div class="category-description">
          <?php echo category_description(); ?>
        </div>
      <?php endif; ?>
    </div>

    <!-- Posts Grid -->
    <div class="category-content">
      <?php if (have_posts()) : ?>

        <?php
        $post_count = 0;
        $featured_post_shown = false;
        ?>

        <!-- Featured Post (First Post) -->
        <?php while (have_posts()) : the_post(); ?>
          <?php $post_count++; ?>

          <?php if ($post_count === 1 && !$featured_post_shown) : ?>
            <div class="featured-post-container">
              <?php get_template_part('template-parts/featured-post-card'); ?>
            </div>
            <?php $featured_post_shown = true; ?>

            <!-- Regular Posts Grid -->
            <div class="posts-grid">

          <?php else : ?>
            <?php get_template_part('template-parts/post-card'); ?>
          <?php endif; ?>

        <?php endwhile; ?>

        <?php if ($featured_post_shown) : ?>
          </div> <!-- Close posts-grid -->
        <?php endif; ?>

        <!-- Pagination -->
        <div class="category-pagination">
          <?php
          $pagination_args = array(
            'mid_size' => 2,
            'prev_text' => '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z" fill="currentColor"/></svg>',
            'next_text' => '<svg width="16" height="16" viewBox="0 0 24 24" fill="none"><path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z" fill="currentColor"/></svg>',
            'type' => 'array'
          );

          $pagination_links = paginate_links($pagination_args);

          if ($pagination_links) :
          ?>
            <nav class="pagination-nav" aria-label="Navegação de páginas">
              <ul class="pagination-list">
                <?php foreach ($pagination_links as $link) : ?>
                  <li class="pagination-item">
                    <?php echo $link; ?>
                  </li>
                <?php endforeach; ?>
              </ul>
            </nav>
          <?php endif; ?>
        </div>

      <?php else : ?>
        <!-- No Posts Found -->
        <div class="no-posts-found">
          <h2>Nenhum post encontrado</h2>
          <p>Não há posts nesta categoria no momento.</p>
          <a href="<?php echo esc_url(home_url('/')); ?>" class="btn-back-home">Voltar ao início</a>
        </div>
      <?php endif; ?>
    </div>

  </div>
</main>

<?php get_footer(); ?>
