document.addEventListener('DOMContentLoaded', function() {
    // Social sharing functionality
    const socialButtons = document.querySelectorAll('.social-btn');

    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add click tracking or analytics here if needed
            console.log('Social share clicked:', this.classList[1]);

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Smooth scroll behavior for sticky elements
    let lastScrollTop = 0;
    const socialSidebar = document.querySelector('.social-share-sticky');

    if (socialSidebar) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Add fade effect based on scroll position
            if (scrollTop > 300) {
                socialSidebar.style.opacity = '1';
            } else {
                socialSidebar.style.opacity = '0.7';
            }

            lastScrollTop = scrollTop;
        });
    }

    // Copy link functionality (could be added as additional social option)
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('Link copiado!');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('Link copiado!');
        }
    }

    // Simple notification system
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #7CD950;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 9999;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Enhanced popular posts interaction - removed transform effects
    const popularPosts = document.querySelectorAll('.popular-post-link');

    popularPosts.forEach(link => {
        // Removed transform effects as requested
        // Only keeping basic interaction tracking if needed
        link.addEventListener('click', function() {
            // Add analytics tracking here if needed
            console.log('Popular post clicked:', this.href);
        });
    });

    // Reading progress indicator (optional enhancement)
    function createReadingProgress() {
        const article = document.querySelector('.single-content');
        if (!article) return;

        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: #7CD950;
            z-index: 9999;
            transition: width 0.1s ease;
        `;

        document.body.appendChild(progressBar);

        window.addEventListener('scroll', function() {
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;

            const articleBottom = articleTop + articleHeight;
            const windowBottom = scrollTop + windowHeight;

            if (scrollTop >= articleTop && scrollTop <= articleBottom) {
                const progress = ((scrollTop - articleTop) / (articleHeight - windowHeight)) * 100;
                progressBar.style.width = Math.min(Math.max(progress, 0), 100) + '%';
            }
        });
    }

    // Initialize reading progress
    createReadingProgress();

    // See Also Ad Sticky Behavior
    function initSeeAlsoAdSticky() {
        const seeAlsoAd = document.querySelector('.see-also-ad');
        const seeAlsoSection = document.querySelector('.see-also-section');

        if (!seeAlsoAd || !seeAlsoSection) return;

        let isSticky = false;

        window.addEventListener('scroll', function() {
            const sectionRect = seeAlsoSection.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Check if user has scrolled to the see-also section
            if (sectionRect.top <= 45 && sectionRect.bottom > 45) {
                if (!isSticky) {
                    seeAlsoAd.style.position = 'sticky';
                    seeAlsoAd.style.top = '45px';
                    isSticky = true;
                }
            } else {
                if (isSticky) {
                    seeAlsoAd.style.position = 'relative';
                    seeAlsoAd.style.top = 'auto';
                    isSticky = false;
                }
            }
        });
    }

    // Initialize see also ad sticky behavior
    initSeeAlsoAdSticky();

    // Table of Contents functionality
    function generateTableOfContents() {
        const tocContainer = document.getElementById('table-of-contents');
        const content = document.querySelector('.single-content');

        if (!tocContainer || !content) return;

        const headings = content.querySelectorAll('h2');

        if (headings.length === 0) {
            // Hide TOC widget if no headings found
            const tocWidget = document.querySelector('.table-of-contents-widget');
            if (tocWidget) {
                tocWidget.style.display = 'none';
            }
            return;
        }

        let tocHTML = '';

        headings.forEach((heading, index) => {
            // Create unique ID for each heading
            const headingId = `heading-${index}`;
            heading.id = headingId;

            // Get heading level for styling
            const level = heading.tagName.toLowerCase();

            // Create TOC item
            tocHTML += `
                <div class="toc-item toc-${level}">
                    <a href="#${headingId}" class="toc-link" data-target="${headingId}">
                        ${heading.textContent.trim()}
                    </a>
                </div>
            `;
        });

        tocContainer.innerHTML = tocHTML;

        // Add click handlers for smooth scrolling
        const tocLinks = tocContainer.querySelectorAll('.toc-link');
        tocLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('data-target');
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    // Calculate offset for fixed header
                    const headerOffset = 160;
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    // Update active state
                    tocLinks.forEach(l => l.closest('.toc-item').classList.remove('active'));
                    this.closest('.toc-item').classList.add('active');
                }
            });
        });

        // Highlight current section on scroll
        function updateActiveSection() {
            const scrollPosition = window.scrollY + 200;

            let currentSection = null;
            headings.forEach(heading => {
                const rect = heading.getBoundingClientRect();
                const headingTop = rect.top + window.scrollY;

                if (headingTop <= scrollPosition) {
                    currentSection = heading.id;
                }
            });

            // Update active link
            tocLinks.forEach(link => {
                link.closest('.toc-item').classList.remove('active');
                if (link.getAttribute('data-target') === currentSection) {
                    link.closest('.toc-item').classList.add('active');
                }
            });
        }

        // Throttled scroll listener
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateActiveSection();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);

        // Set initial active state
        updateActiveSection();
    }

    // Initialize Table of Contents
    generateTableOfContents();

});

// Summary toggle function (global scope for onclick)
function toggleSummary() {
    const summaryHeader = document.querySelector('.summary-header');
    const summaryContent = document.getElementById('summaryContent');
    const summaryTitle = document.querySelector('.summary-title');

    if (!summaryHeader || !summaryContent || !summaryTitle) return;

    const isActive = summaryContent.classList.contains('active');

    if (isActive) {
        // Close summary
        summaryContent.classList.remove('active');
        summaryHeader.classList.remove('active');
        summaryTitle.textContent = 'Ver resumo';
    } else {
        // Open summary
        summaryContent.classList.add('active');
        summaryHeader.classList.add('active');
        summaryTitle.textContent = 'Resumo';
    }
}
