/* Header Styles */
.site-header {
  background: #0B3B4F;
  color: #fff;
  font-family: 'Inter', sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Header scroll states */
.site-header.header-hidden {
  transform: translateY(-100%);
}

.site-header.header-scrolled {
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  background: rgba(11, 59, 79, 0.95);
  backdrop-filter: blur(10px);
}

/* Body padding to compensate for fixed header */
body {
  padding-top: 120px; /* Adjust based on header height */
}

@media (max-width: 768px) {
  body {
    padding-top: 100px;
  }
}

@media (max-width: 480px) {
  body {
    padding-top: 90px;
  }
}

/* Header Top Row */
.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid;
  border-image: linear-gradient(269deg, rgba(57, 117, 151, 0) 3.58%, #406880 50.52%, rgba(57, 117, 151, 0) 96.82%) 1;
}

/* Menu Trigger */
.menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.menu-trigger:hover {
  background-color: rgba(255,255,255,0.1);
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.hamburger span {
  width: 18px;
  height: 2px;
  background-color: #fff;
  border-radius: 1px;
  transition: all 0.3s;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

/* Site Logo */
.site-logo {
  flex: 1;
  display: flex;
  justify-content: center;
}

.site-logo a {
  display: block;
}

.site-logo img {
  height: 32px;
  width: auto;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-btn {
  background: none;
  border: none;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: rgba(255,255,255,0.1);
}

.search-btn svg {
  stroke: #fff;
}

/* Desktop Search Form */
.desktop-search-form {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  min-width: 400px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.search-form-advanced {
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-input-container input:focus {
  outline: none;
  border-color: #7CD950;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(124, 217, 80, 0.1);
}

.search-submit-btn {
  background: #7CD950;
  color: #fff;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-submit-btn:hover {
  background: #5fb83a;
  transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
  padding: 20px;
}

.search-categories,
.recent-searches {
  margin-bottom: 20px;
}

.search-categories:last-child,
.recent-searches:last-child {
  margin-bottom: 0;
}

.search-suggestions h4 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-tag {
  background: #f0f7ff;
  color: #0B3B4F;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.category-tag:hover {
  background: #7CD950;
  color: #fff;
  transform: translateY(-1px);
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-item {
  color: #666;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.recent-item:hover {
  background: #f8f9fa;
  color: #0B3B4F;
}

.recent-item::before {
  content: "🕒";
  margin-right: 8px;
  font-size: 12px;
}

/* Mobile Search Form */
.mobile-search-container {
  padding: 16px 20px 20px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mobile-search-form {
  width: 100%;
}

.mobile-search-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 25px;
  padding: 12px 20px;
  gap: 12px;
  transition: all 0.3s ease;
}

.mobile-search-wrapper:focus-within {
  border-color: rgba(255,255,255,0.4);
  background: rgba(255,255,255,0.15);
}

.search-icon {
  color: rgba(255,255,255,0.6);
  flex-shrink: 0;
}

.mobile-search-wrapper input {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 16px;
  outline: none;
  font-weight: 400;
}

.mobile-search-wrapper input::placeholder {
  color: rgba(255,255,255,0.6);
}

/* Header Navigation */
.header-nav {
  display: flex;
  align-items: center;
  padding: 12px 0;
  position: relative;
  overflow: hidden;
}

.header-nav::-webkit-scrollbar {
  display: none;
}

/* Gradient overlay for smooth fade effect */
.header-nav::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, #0B3B4F 70%);
  pointer-events: none;
  z-index: 5;
}

/* Navigation Arrows - positioned after menu items */
.nav-arrows {
  display: flex;
  gap: 4px;
  margin-left: 16px;
  flex-shrink: 0;
  z-index: 10;
  position: relative;
}

.nav-arrow {
  background: none;
  border: none;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.nav-arrow:hover {
  background-color: rgba(255,255,255,0.1);
}

/* Main Navigation */
.main-navigation {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  position: relative;
  flex: 1;
  max-width: calc(100% - 200px); /* Reserve space for TV button and arrows */
}

.main-navigation::-webkit-scrollbar {
  display: none;
}

.main-menu-list {
  display: flex;
  align-items: center;
  gap: 24px;
  list-style: none;
  margin: 0;
  padding: 0;
  min-width: max-content; /* Ensure items don't wrap */
}

.main-menu-list li {
  position: relative;
  white-space: nowrap;
}

.main-menu-list a {
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s;
  display: block;
}

.main-menu-list a:hover {
  background-color: rgba(255,255,255,0.1);
  color: #7CD950;
}

/* Submenu Styles */
.main-menu-list .sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #0B3B4F;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  list-style: none;
  margin: 0;
  padding: 8px 0;
}

.main-menu-list li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.main-menu-list .sub-menu li {
  width: 100%;
}

.main-menu-list .sub-menu a {
  padding: 10px 16px;
  font-size: 13px;
  border-radius: 0;
}

.main-menu-list .sub-menu a:hover {
  background-color: rgba(124, 217, 80, 0.1);
}

/* TV Live Button */
.tv-live-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(124, 217, 80, 0.2);
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #7CD950;
  transition: all 0.2s;
  flex-shrink: 0;
  margin-left: 16px;
}

.tv-live-btn:hover {
  background: #7CD950;
  color: #0B3B4F;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #7CD950;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100%;
  background: #0B3B4F;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mobile-menu-overlay.active .mobile-menu-content {
  transform: translateX(0);
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  flex-shrink: 0;
}

.mobile-logo {
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.mobile-logo:hover {
  opacity: 0.8;
}

.mobile-menu-header span {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 4px;
}



/* Mobile Navigation */
.mobile-nav {
  position: relative;
  overflow: visible;
  flex: 1;
}

.mobile-menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
  transition: transform 0.3s ease;
}

.mobile-menu-list li {
  position: relative;
}

.mobile-menu-list > li > a,
.mobile-menu-list > li > .submenu-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  text-decoration: none;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 400;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  transition: background-color 0.2s ease;
}

.mobile-menu-list > li > a:hover,
.mobile-menu-list > li > .submenu-trigger:hover {
  background-color: rgba(255,255,255,0.05);
}

/* Submenu trigger styling */
.submenu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.submenu-arrow {
  color: rgba(255,255,255,0.6);
  transition: transform 0.3s ease;
}

/* TV Live link styling */
.tv-live-link {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Mobile Submenu */
.mobile-submenu {
  position: fixed;
  top: 80px;
  left: 0;
  width: 100%;
  height: calc(100vh - 80px);
  background: #0B3B4F;
  list-style: none;
  margin: 0;
  padding: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  display: block;
  overflow-y: auto;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
}

.mobile-submenu.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Submenu back button */
.submenu-back {
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #fff;
  padding: 16px 20px;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255,255,255,0.05);
}

/* Submenu main link */
.submenu-main {
  color: #7CD950 !important;
  font-weight: 600 !important;
  background: rgba(124, 217, 80, 0.1) !important;
}

/* Submenu items */
.mobile-submenu li:not(.submenu-back) a {
  display: block;
  color: #B8C5D1;
  text-decoration: none;
  padding: 14px 20px;
  font-size: 15px;
  border-bottom: 1px solid rgba(255,255,255,0.05);
  transition: all 0.2s ease;
}

.mobile-submenu li:not(.submenu-back) a:hover {
  background-color: rgba(124, 217, 80, 0.1);
  color: #7CD950;
}

.tv-live-mobile {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(124, 217, 80, 0.2);
  border: 1px solid #7CD950;
  margin-top: 16px;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #7CD950;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-nav {
    padding: 8px 0;
  }

  .main-menu-list {
    gap: 16px;
  }

  .main-menu-list a {
    font-size: 13px;
    padding: 6px 10px;
  }

  .tv-live-btn {
    font-size: 13px;
    padding: 6px 12px;
  }

  .nav-arrow {
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .header-top {
    padding: 10px 0;
  }

  .site-logo img {
    height: 28px;
  }

  .menu-text {
    font-size: 13px;
  }

  .main-menu-list {
    gap: 12px;
  }

  .main-menu-list a {
    font-size: 12px;
    padding: 6px 8px;
  }

  .tv-live-btn {
    font-size: 12px;
    padding: 6px 10px;
  }
}

/* Additional Header Enhancements */
.site-logo svg {
  transition: transform 0.2s ease;
}

.site-logo:hover svg {
  transform: scale(1.05);
}

/* Smooth scrolling for navigation - merged with main navigation styles above */

/* Focus states for accessibility */
.menu-trigger:focus,
.search-btn:focus,
.nav-arrow:focus,
.main-menu-list a:focus,
.mobile-menu-list a:focus,
.tv-live-btn:focus,
.desktop-search-form input:focus,
.mobile-search-form input:focus,
.submenu-toggle:focus {
  outline: 2px solid #7CD950;
  outline-offset: 2px;
}

/* Loading state for navigation arrows */
.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Better mobile menu backdrop */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
}

/* Header z-index already defined above */

.post-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  margin-top: 2rem;
}

.post-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}

.post-card:hover {
  transform: translateY(-3px);
}

.post-card a {
  text-decoration: none;
  color: inherit;
  display: block;
}

.post-card .thumb img {
  width: 100%;
  display: block;
  height: auto;
}

.post-card .post-info {
  padding: 16px;
}

.post-card .title {
  font-size: 1.125rem;
  margin: 0 0 0.5rem;
}

.post-card .excerpt {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.4;
}

.content-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
}

main.container {
  flex: 1 1 700px;
}

.sidebar {
  flex: 0 0 300px;
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
}

.widget {
  margin-bottom: 2rem;
}

.widget-title {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  border-bottom: 2px solid #eee;
}

.single-post .featured-image img {
  width: 100%;
  border-radius: 6px;
  margin-bottom: 1.5rem;
}

.entry-header {
  margin-bottom: 1rem;
}

.entry-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.post-meta {
  color: #777;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.entry-content {
  font-size: 1.05rem;
  line-height: 1.7;
}

.entry-content p {
  margin-bottom: 1.5rem;
}

.entry-content blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1rem;
  color: #555;
  font-style: italic;
  margin: 1.5rem 0;
}

.entry-content a {
  color: #0066cc;
  text-decoration: underline;
}

.post-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.post-navigation a {
  color: #333;
  text-decoration: none;
  font-weight: bold;
}

.recommended-block {
  margin: 3rem auto;
}
.block-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #ccc;
  padding-bottom: .5rem;
}
.recommended-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.recommended-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.recommended-card:hover {
  transform: translateY(-2px);
}
.recommended-card a {
  color: inherit;
  text-decoration: none;
  display: block;
}
.recommended-card .thumb img {
  width: 100%;
  height: auto;
  display: block;
}
.recommended-card .info {
  padding: 12px;
}
.recommended-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.recommended-card time {
  font-size: 0.85rem;
  color: #666;
}

.latest-posts-block {
  margin: 3rem auto;
}
.latest-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.latest-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.latest-card:hover {
  transform: translateY(-2px);
}
.latest-card a {
  color: inherit;
  text-decoration: none;
  display: block;
}
.latest-card .thumb img {
  width: 100%;
  height: auto;
  display: block;
}
.latest-card .info {
  padding: 12px;
}
.latest-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.latest-card time {
  font-size: 0.85rem;
  color: #666;
}
.load-more {
  margin: 2rem auto 0;
  display: block;
  background: #0b3d5c;
  color: #fff;
  font-size: 0.95rem;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  cursor: pointer;
}

.curiosities-block {
  margin: 3rem auto;
}
.curiosity-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.curiosity-card {
  display: flex;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.curiosity-card:hover {
  transform: scale(1.01);
}
.curiosity-card a {
  color: inherit;
  text-decoration: none;
  display: flex;
  width: 100%;
}
.curiosity-card .thumb img {
  width: 160px;
  height: 100%;
  object-fit: cover;
  display: block;
}
.curiosity-card .info {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.curiosity-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.curiosity-card time {
  font-size: 0.85rem;
  color: #666;
}
@media (max-width: 600px) {
  .curiosity-card {
    flex-direction: column;
  }
  .curiosity-card .thumb img {
    width: 100%;
    height: auto;
  }
}

.site-wrapper {
  width: 100%;
}

/* Footer Styles */
.site-footer {
  background: #2C4A5C;
  color: #fff;
  padding: 60px 0 0;
  margin-top: 3rem;
  font-family: 'Inter', sans-serif;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
}

/* Footer Brand */
.footer-brand {
  max-width: 500px;
}

.footer-logo h2 {
  font-size: 48px;
  font-weight: bold;
  margin: 0 0 24px 0;
  line-height: 1;
}

.logo-the {
  color: #7CD950;
}

.logo-agribiz {
  color: #fff;
}

.footer-description p {
  color: #B8C5D1;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.partner-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 6px;
  transition: all 0.3s ease;
  margin-bottom: 32px;
}

.partner-link:hover {
  background: rgba(124, 217, 80, 0.1);
  border-color: #7CD950;
  color: #7CD950;
}

.footer-social {
  display: flex;
  gap: 16px;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  color: #B8C5D1;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  background: #7CD950;
  color: #2C4A5C;
  transform: translateY(-2px);
}

/* Footer Menu */
.footer-menu h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 32px 0;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.footer-menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px 32px;
}

.footer-menu-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-menu-column a {
  color: #B8C5D1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-menu-column a:hover {
  color: #7CD950;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0;
  border-top: 1px solid rgba(255,255,255,0.2);
}

.footer-bottom-left p {
  color: #B8C5D1;
  font-size: 14px;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 32px;
}

.footer-bottom-links a {
  color: #B8C5D1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #7CD950;
}

.footer-bottom-logo img {
  height: 24px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.footer-bottom-logo:hover img {
  opacity: 1;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-logo h2 {
    font-size: 36px;
  }

  .footer-menu-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .site-footer {
    padding: 40px 0 0;
  }

  .footer-logo h2 {
    font-size: 28px;
  }

  .footer-description p {
    font-size: 14px;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-bottom-links {
    gap: 12px;
  }

  .footer-bottom-links a {
    font-size: 12px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* Remove old header styles that conflict */
.site-footer {
  width: 100%;
  background-color: #002e47;
  color: #fff;
}

.single-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}
.sidebar {
  background: #f9f9f9;
  padding: 1rem;
}
.sidebar .widget {
  margin-bottom: 1.5rem;
}
.sidebar .widget-title {
  font-weight: bold;
  margin-bottom: 0.75rem;
}
@media (max-width: 768px) {
  .single-layout {
    grid-template-columns: 1fr;
  }
  .sidebar {
    padding: 0;
  }
}

/* Breadcrumb */
.breadcrumb-nav {
  padding: 20px 0;
  font-size: 14px;
  color: #666;
}

.breadcrumb-nav a {
  color: #666;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  color: #333;
}

.breadcrumb-separator {
  margin: 0 10px;
}

/* Single Post Header - Full Width */
.single-post-header {
  padding: 40px 0 30px 0;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 40px;
}

.post-category-header {
  margin-bottom: 20px;
}

.category-link-header {
  background: #007cba;
  color: white;
  padding: 7px 11px 6px;
  border-radius: 20px;
  text-decoration: none;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  font-family: 'Inter', sans-serif;
}

.category-link-header:hover {
  background: #005a87;
  color: white;
}

.single-title-header {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  margin: 0 0 20px 0;
  max-width: 100%;
  font-family: 'Inter', sans-serif;
}

.single-subtitle-header {
  font-size: 20px;
  line-height: 1.5;
  color: #666;
  margin-bottom: 30px;
  font-weight: 400;
}

.post-meta-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.author-info-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar-header {
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.author-details-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name-header {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.post-date-header {
  color: #666;
  font-size: 12px;
}

/* Single Post Layout */
.single-post-layout {
  display: grid;
  grid-template-columns: 80px 1fr 320px;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px 20px;
  position: relative;
}

/* Social Share Sidebar (Sticky) */
.social-share-sidebar {
  position: relative;
}

.social-share-sticky {
  position: sticky;
  top: 140px; /* Account for fixed header */
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 10;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

.social-btn.whatsapp {
  background: #25D366;
  color: #fff;
}

.social-btn.facebook {
  background: #1877F2;
  color: #fff;
}

.social-btn.twitter {
  background: #1DA1F2;
  color: #fff;
}

.social-btn.linkedin {
  background: #0A66C2;
  color: #fff;
}

/* Main Content */
.single-main-content {
  min-width: 0; /* Prevent grid overflow */
}

.single-article {
  background: #fff;
  overflow: hidden;
}

/* Post Header */
.single-header {
  padding: 40px 40px 0;
}

.post-category {
  margin-bottom: 16px;
}

.category-link {
  display: inline-block;
  background: #0B3B4F;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: background-color 0.3s ease;
}

.category-link:hover {
  background: #7CD950;
}

.single-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: #1a1a1a;
  margin: 0 0 20px 0;
  font-family: 'Inter', sans-serif;
}

.single-subtitle {
  font-size: 1.25rem;
  line-height: 1.5;
  color: #666;
  margin-bottom: 30px;
  font-weight: 400;
}

.post-meta-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 30px;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 30px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 14px;
}

.post-date {
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-date::before {
  content: "🕒";
  font-size: 12px;
}

/* Featured Image */
.single-featured-image {
  /* margin: 0 40px 40px; */
}

.featured-img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  display: block;
}

/* Content */
.single-content {
  font-size: 20px;
  font-family: 'Figtree', sans-serif;
  font-weight: 400;
  line-height: 38px;
  color: #333;
}

.single-content p {
  margin-bottom: 1.5rem;
}

.single-content h1,
.single-content h2,
.single-content h3,
.single-content h4,
.single-content h5,
.single-content h6 {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: 40px;
  line-height: 48px;
  margin: 2rem 0 1rem;
  color: #1a1a1a;
}

.single-content img {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  min-height: 100% !important;
  width: 100%;
  aspect-ratio: 1 / 1 !important;
  max-height: 160px;
  max-width: 160px;
  height: auto;

}

/* Single Sidebar */
.single-sidebar {
  position: relative;
}

.sidebar-content {
  position: sticky;
  top: 45px;
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.sidebar-title {
  font-size: 28px;
  font-weight: 700;
  color: #007cba;
  margin: 0 0 20px 0;
  padding: 0;
  border-bottom: none;
  font-family: 'Inter', sans-serif;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.popular-posts {
  display: flex;
  flex-direction: column;
  gap: 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.popular-post-item {
  margin-bottom: 20px;
  padding: 0;
  border-bottom: none;
  background: transparent;
}

.popular-post-item:hover {
  background: transparent;
}

.popular-post-item:first-child {
  margin-bottom: 24px;
  padding: 0;
  border-bottom: none;
}

.popular-post-item:first-child .popular-post-link {
  display: block;
}

.popular-post-item:first-child .popular-post-thumb {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.popular-post-item:first-child  .popular-post-content{
  padding: 24px;
  border: 1px solid #e5e5e5;
  border-top: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.popular-post-item:first-child .popular-post-category {
  display: inline-block;
  background: #007cba;
  color: #fff;
  font-size: 12px;
  font-weight: 700;
  padding: 4px 12px;
  border-radius: 4px;
  text-transform: uppercase;
  margin-bottom: 12px;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
}

.popular-post-item:first-child .popular-post-title {
  font-size: 20px;
  line-height: 26px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #333;
}

.popular-post-item:first-child .popular-post-excerpt {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin: 0;
  font-family: 'Figtree', sans-serif;
}

.popular-post-item:not(:first-child) {
  border-bottom: none;
  padding: 0;
}

.popular-post-item:last-child {
  border-bottom: none;
  border-radius: 0;
  margin-bottom: 0;
}

.popular-post-link {
  display: flex;
  gap: 24px;
  text-decoration: none;
  color: inherit;
  align-items: flex-start;
}

.popular-post-thumb {
  flex-shrink: 0;
  width: 96px;
  height: auto;
  border-radius: 6px;
  line-height: 0;
  overflow: hidden;
}

.popular-post-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.popular-post-content {
  flex: 1;
  min-width: 0;
}

.popular-post-category {
  display: inline-block;
  background: transparent;
  color: #007cba;
  font-size: 11px;
  font-weight: 700;
  padding: 0;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  font-family: 'Inter', sans-serif;
}

.popular-post-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  color: #333;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

/* Related Posts Component */
.related-posts-component {
  padding: 0 0 30px 0;
  border-bottom: 2px solid #e5e5e5;
}

.related-posts-title {
  font-weight: 700 !important;
  font-size: 20px !important;
  line-height: 24px !important;
  color: #007cba;
  margin: 0 0 24px 0 !important;
  text-align: left;
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap;
  gap: 1rem !important;
}

.related-posts-title::after {
  content: '';
  height: 2px;
  background: #e5e5e5;
  width: 100%;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.related-post-item {
  background: transparent;
  border-radius: 0;
  overflow: visible;
  box-shadow: none;
  transition: none;
  display: flex;
  gap: 16px;
}

.related-post-item:hover {
  transform: none;
}

.related-post-link {
  display: flex;
  text-decoration: none;
  color: inherit;
  width: 100%;
}

.related-post-image {
  width: 152px;
  height: 107px;
  object-fit: cover;
  border-radius: 0;
  flex-shrink: 0;
}

.related-post-content {
  padding: 0 24px;
  border: 1px solid #e5e5e5;
  border-left: none;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (max-width: 768px) {
  .related-post-content {
    padding: 0 7px 7px 7px;
  }
}

.related-post-category {
  display: inline-block;
  background: transparent;
  color: #007cba;
  font-size: 12px;
  font-weight: 700;
  padding: 0;
  text-transform: uppercase;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
  line-height: 20px;
}

.related-post-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 16px !important;
  line-height: 22px !important;
  color: #1a1a1a;
  margin: 0 !important;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Author Box */
.author-box {
  margin: 40px 0;
  padding: 30px 40px;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.author-box-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.author-box-avatar {
  flex-shrink: 0;
}

.author-box-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.author-box-info {
  flex: 1;
}

.author-box-name {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: 24px;
  line-height: 32px;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.author-box-description {
  font-family: 'Figtree', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #666;
  margin: 0;
}

/* See Also Section */
.see-also-section {
  margin: 60px 0 40px 0;
  padding: 40px 0;
}

.see-also-container {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.see-also-content {
  flex: 1;
}

.see-also-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  font-size: 32px;
  line-height: 40px;
  color: #1a1a1a;
  margin: 0 0 30px 0;
}

.see-also-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.see-also-item {
  display: flex;
  gap: 24px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e5e5;
  max-width: 100%;
}

.see-also-item:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.see-also-item:last-child {
  padding-bottom: 0;
}

.see-also-image {
  flex-shrink: 0;
  width: 390px;
  height: 220px;
  min-width: 390px;
  overflow: hidden;
  border-radius: 12px 0 0 12px;
}

.see-also-image img {
  width: 390px;
  height: 220px;
  object-fit: cover;
  object-position: center;
}

.see-also-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.see-also-content h4{
  margin: 0 24px 0 0;
}

.see-also-category {
  display: inline-block;
  background: transparent;
  color: #007cba;
  font-size: 14px;
  line-height: 15px;
  font-weight: 700;
  padding: 0;
  border-radius: 0;
  text-transform: uppercase;
  margin-bottom: 8px;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
}

.see-also-post-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
  color: #515150;
  margin: 0;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.see-also-post-title:hover {
  color: #007cba;
}

.see-also-sidebar {
  position: relative;
}

.see-also-ad {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  position: sticky;
  top: 45px;
  width: 100%;
  transition: all 0.3s ease;
}

.see-also-ad:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* Post Footer */
.single-footer {
  padding: 40px;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.post-navigation {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.nav-previous,
.nav-next {
  flex: 1;
}

.nav-previous a,
.nav-next a {
  display: block;
  padding: 16px 20px;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-previous a:hover,
.nav-next a:hover {
  background: #0B3B4F;
  color: #fff;
  border-color: #0B3B4F;
}

.nav-next {
  text-align: right;
}

/* Responsive Design for Single Post */
@media (max-width: 1200px) {
  .single-post-layout {
    grid-template-columns: 60px 1fr 280px;
    gap: 30px;
  }

  .container {
    padding: 0 16px;
  }

  .social-btn {
    width: 40px;
    height: 40px;
  }

  .single-title-header {
    font-size: 40px;
  }

  .single-subtitle-header {
    font-size: 18px;
  }

  .single-header {
    padding: 30px 30px 0;
  }

  .single-title {
    font-size: 2.2rem;
  }

  .single-featured-image {
    /* margin: 0 30px 30px; */
  }

  .single-content {
    padding: 0 30px 30px;
  }

  .single-footer {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .single-post-header {
    padding: 30px 0 40px 0;
  }

  .single-title-header {
    font-size: 32px;
  }

  .single-subtitle-header {
    font-size: 16px;
  }

  .post-meta-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .single-post-layout {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-top: 20px;
    padding: 0 16px;
  }

  /* Hide social sidebar on mobile, show at bottom */
  .social-share-sidebar {
    order: 3;
    margin-top: 30px;
  }

  .social-share-sticky {
    position: static;
    flex-direction: row;
    justify-content: center;
    gap: 16px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .single-main-content {
    order: 1;
  }

  .single-sidebar {
    order: 2;
  }

  .single-header {
    padding: 20px 20px 0;
  }

  .single-title {
    font-size: 1.8rem;
  }

  .single-subtitle {
    font-size: 1.1rem;
  }

  .post-meta-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .single-featured-image {
    /* margin: 0 20px 20px; */
  }

  .single-content {
    padding: 0 20px 20px;
    font-size: 18px;
    line-height: 32px;
  }

  .single-content h1,
  .single-content h2,
  .single-content h3,
  .single-content h4,
  .single-content h5,
  .single-content h6 {
    font-size: 28px;
    line-height: 36px;
  }

  /* Related Posts Mobile */
  .related-posts-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .related-posts-title {
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  .related-posts-title::after {
    bottom: -6px;
  }

  .related-post-item {
    flex-direction: row;
    gap: 12px;
  }

  .related-post-link {
    flex-direction: row;
  }

  .related-post-image {
    width: 120px !important;
    height: 84px !important;
  }

  .related-post-title {
    font-size: 14px;
    line-height: 18px;
  }

  .related-post-category {
    font-size: 11px;
  }

  /* Popular Posts Mobile */
  .popular-post-item:first-child .popular-post-thumb {
    height: 140px;
  }

  .popular-post-item:first-child .popular-post-title {
    font-size: 16px;
    line-height: 22px;
  }

  /* Author Box Mobile */
  .author-box {
    margin: 30px 0;
    padding: 20px;
  }

  .author-box-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .author-box-name {
    font-size: 20px;
    line-height: 28px;
  }

  .author-box-description {
    font-size: 14px;
    line-height: 20px;
  }

  /* See Also Mobile */
  .see-also-section {
    margin: 40px 0 20px 0;
    padding: 20px 0;
  }

  .see-also-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 16px;
  }

  .see-also-spacer {
    display: none;
  }

  .see-also-title {
    font-size: 24px;
    line-height: 32px;
  }

  .see-also-item {
    flex-direction: column;
    gap: 24px;
  }

  .see-also-image {
    width: 100%;
    height: 200px;
    min-width: auto;
    border-radius: 12px 12px 0 0;
  }

  .see-also-image img {
    width: 390px;
    height: 220px;
    max-width: 100%;
    max-height: 200px;
    object-fit: cover;
    object-position: center;
  }

  .see-also-content {
    padding: 16px;
  }

  .see-also-post-title {
    font-size: 16px;
    line-height: 22px;
  }

  .see-also-sidebar {
    position: static;
    order: -1;
  }

  .see-also-ad {
    position: static;
    margin-bottom: 30px;
    min-height: 250px;
  }

  .single-footer {
    padding: 20px;
  }

  .post-navigation {
    flex-direction: column;
  }

  .nav-next {
    text-align: left;
  }

  .sidebar-content {
    position: static;
  }
}

/* Tablet Responsive */
@media (max-width: 1200px) {
  .see-also-container {
    grid-template-columns: 1fr 280px;
    gap: 30px;
    padding: 0 16px;
  }

  .see-also-ad {
    min-height: 300px;
  }
}

@media (max-width: 900px) {
  .see-also-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 16px;
  }

  .see-also-spacer {
    display: none;
  }

  .see-also-sidebar {
    order: -1;
  }

  .see-also-ad {
    position: static;
    min-height: 200px;
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .single-post-header {
    padding: 20px 0 30px 0;
  }

  .single-title-header {
    font-size: 24px;
  }

  .single-subtitle-header {
    font-size: 14px;
  }

  .single-post-layout {
    padding: 0 12px;
  }

  .single-header {
    padding: 16px 16px 0;
  }

  .single-title {
    font-size: 1.5rem;
  }

  .single-subtitle {
    font-size: 1rem;
  }

  .single-featured-image {
    /* margin: 0 16px 16px; */
  }

  .single-content {
    padding: 0 16px 16px;
    font-size: 16px;
    line-height: 28px;
  }

  .single-content h1,
  .single-content h2,
  .single-content h3,
  .single-content h4,
  .single-content h5,
  .single-content h6 {
    font-size: 24px;
    line-height: 32px;
  }

  .single-footer {
    padding: 16px;
  }

  .social-btn {
    width: 44px;
    height: 44px;
  }

  .sidebar-content {
    padding: 0;
  }

  .sidebar-title {
    padding: 16px;
    font-size: 20px;
  }

  .popular-post-item {
    padding: 16px;
  }

  .popular-post-thumb {
    width: 60px;
    height: 45px;
  }

  .popular-post-title {
    font-size: 13px;
  }
}


