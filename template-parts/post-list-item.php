<?php
/**
 * Template part for displaying post list items
 * Used in category pages for posts 6+ following "Vej<PERSON> Mai<PERSON>" pattern
 */
?>

<article class="see-also-item">
  <a href="<?php the_permalink(); ?>" class="see-also-link">
    
    <!-- Post Thumbnail -->
    <div class="see-also-image">
      <?php if (has_post_thumbnail()) : ?>
        <?php the_post_thumbnail('medium', array('class' => 'see-also-thumb')); ?>
      <?php else : ?>
        <div class="see-also-thumb-placeholder">
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="#ccc"/>
          </svg>
        </div>
      <?php endif; ?>
    </div>

    <!-- Post Content -->
    <div class="see-also-content">
      <!-- Category Badge -->
      <?php
      $categories = get_the_category();
      if (!empty($categories)) :
        $primary_category = $categories[0];
      ?>
        <span class="see-also-category"><?php echo esc_html($primary_category->name); ?></span>
      <?php endif; ?>
      
      <h3 class="see-also-title"><?php the_title(); ?></h3>
      
      <div class="see-also-excerpt">
        <?php 
        $excerpt = get_the_excerpt();
        if (strlen($excerpt) > 100) {
          $excerpt = substr($excerpt, 0, 100) . '...';
        }
        echo esc_html($excerpt);
        ?>
      </div>
      
      <div class="see-also-meta">
        <time class="see-also-date" datetime="<?php echo get_the_date('c'); ?>">
          <?php echo get_the_date('d/m/Y'); ?>
        </time>
        
        <?php if (function_exists('mr9_get_post_views')) : ?>
          <span class="see-also-views">
            <?php echo mr9_get_post_views(get_the_ID()); ?> visualizações
          </span>
        <?php endif; ?>
      </div>
    </div>
  </a>
</article>
