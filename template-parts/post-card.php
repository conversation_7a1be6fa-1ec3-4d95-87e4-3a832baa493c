<?php
/**
 * Template part for displaying post cards
 * Used in category pages, archive pages, and search results
 */
?>

<article class="post-card">
  <a href="<?php the_permalink(); ?>" class="post-card-link">
    
    <!-- Post Thumbnail -->
    <div class="post-card-image">
      <?php if (has_post_thumbnail()) : ?>
        <?php the_post_thumbnail('medium_large', array('class' => 'card-thumbnail')); ?>
      <?php else : ?>
        <div class="card-thumbnail-placeholder">
          <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="#ccc"/>
          </svg>
        </div>
      <?php endif; ?>
      
      <!-- Category Badge -->
      <?php
      $categories = get_the_category();
      if (!empty($categories)) :
        $primary_category = $categories[0];
      ?>
        <span class="post-card-category"><?php echo esc_html($primary_category->name); ?></span>
      <?php endif; ?>
    </div>

    <!-- Post Content -->
    <div class="post-card-content">
      <h2 class="post-card-title"><?php the_title(); ?></h2>
      
      <div class="post-card-excerpt">
        <?php 
        $excerpt = get_the_excerpt();
        if (strlen($excerpt) > 120) {
          $excerpt = substr($excerpt, 0, 120) . '...';
        }
        echo esc_html($excerpt);
        ?>
      </div>
      
      <div class="post-card-meta">
        <time class="post-card-date" datetime="<?php echo get_the_date('c'); ?>">
          <?php echo get_the_date('d/m/Y'); ?>
        </time>
        
        <?php if (function_exists('mr9_get_post_views')) : ?>
          <span class="post-card-views">
            <?php echo mr9_get_post_views(get_the_ID()); ?> visualizações
          </span>
        <?php endif; ?>
      </div>
    </div>
  </a>
</article>
