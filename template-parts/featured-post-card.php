<?php
/**
 * Template part for displaying featured post card (first post in category)
 * Used in category pages following Canal Rural design pattern
 */
?>

<article class="featured-post-card">
  <a href="<?php the_permalink(); ?>" class="featured-post-link">
    
    <!-- Featured Post Image -->
    <div class="featured-post-image">
      <?php if (has_post_thumbnail()) : ?>
        <?php the_post_thumbnail('large', array('class' => 'featured-thumbnail')); ?>
      <?php else : ?>
        <div class="featured-thumbnail-placeholder">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="#ccc"/>
          </svg>
        </div>
      <?php endif; ?>
      
      <!-- Category Badge -->
      <?php
      $categories = get_the_category();
      if (!empty($categories)) :
        $primary_category = $categories[0];
      ?>
        <span class="featured-post-category"><?php echo esc_html($primary_category->name); ?></span>
      <?php endif; ?>
    </div>

    <!-- Featured Post Content -->
    <div class="featured-post-content">
      <h2 class="featured-post-title"><?php the_title(); ?></h2>
      
      <div class="featured-post-excerpt">
        <?php 
        $excerpt = get_the_excerpt();
        if (strlen($excerpt) > 250) {
          $excerpt = substr($excerpt, 0, 250) . '...';
        }
        echo esc_html($excerpt);
        ?>
      </div>
      
      <div class="featured-post-meta">
        <time class="featured-post-date" datetime="<?php echo get_the_date('c'); ?>">
          <?php echo get_the_date('d/m/Y'); ?>
        </time>
        
        <?php if (function_exists('mr9_get_post_views')) : ?>
          <span class="featured-post-views">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" fill="none"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
            </svg>
            <?php echo mr9_get_post_views(get_the_ID()); ?>
          </span>
        <?php endif; ?>
        
        <?php
        $author_id = get_the_author_meta('ID');
        $author_name = get_the_author();
        if ($author_name) : ?>
          <span class="featured-post-author">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
            </svg>
            <?php echo esc_html($author_name); ?>
          </span>
        <?php endif; ?>
      </div>
    </div>
  </a>
</article>
