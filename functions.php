<?php
require_once get_template_directory() . '/inc/setup.php';
require_once get_template_directory() . '/inc/enqueue.php';

// Custom Walker for Main Navigation with submenu support
class MR9_Main_Walker_Nav_Menu extends Walker_Nav_Menu {
  function start_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "\n$indent<ul class=\"sub-menu\">\n";
  }

  function end_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "$indent</ul>\n";
  }

  function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
    $indent = ($depth) ? str_repeat("\t", $depth) : '';
    $classes = empty($item->classes) ? array() : (array) $item->classes;
    $classes[] = 'menu-item-' . $item->ID;

    $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
    $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

    $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args);
    $id = $id ? ' id="' . esc_attr($id) . '"' : '';

    $output .= $indent . '<li' . $id . $class_names .'>';

    $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
    $attributes .= ! empty($item->target) ? ' target="' . esc_attr($item->target) .'"' : '';
    $attributes .= ! empty($item->xfn) ? ' rel="'    . esc_attr($item->xfn) .'"' : '';
    $attributes .= ! empty($item->url) ? ' href="'   . esc_attr($item->url) .'"' : '';

    $item_output = isset($args->before) ? $args->before : '';
    $item_output .= '<a' . $attributes .'>';
    $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
    $item_output .= '</a>';
    $item_output .= isset($args->after) ? $args->after : '';

    $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
  }

  function end_el(&$output, $item, $depth = 0, $args = null) {
    $output .= "</li>\n";
  }
}

// Custom Walker for Mobile Navigation with submenu support (Canal Rural style)
class MR9_Mobile_Walker_Nav_Menu extends Walker_Nav_Menu {
  private $parent_item = null;

  function start_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "\n$indent<ul class=\"mobile-submenu\" data-depth=\"$depth\">\n";

    // Add back button for level 1+
    if ($depth === 0) {
      $output .= $indent . "\t<li class=\"submenu-back\">\n";
      $output .= $indent . "\t\t<button class=\"back-button\">\n";
      $output .= $indent . "\t\t\t<svg width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n";
      $output .= $indent . "\t\t\t\t<path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n";
      $output .= $indent . "\t\t\t</svg>\n";
      $output .= $indent . "\t\t\tVoltar\n";
      $output .= $indent . "\t\t</button>\n";
      $output .= $indent . "\t</li>\n";

      // Add parent item link as first submenu item (Canal Rural style)
      if ($this->parent_item) {
        $output .= $indent . "\t<li>\n";
        $output .= $indent . "\t\t<a href=\"" . esc_attr($this->parent_item->url) . "\" class=\"submenu-main\">\n";
        $output .= $indent . "\t\t\tTudo sobre " . esc_html($this->parent_item->title) . "\n";
        $output .= $indent . "\t\t</a>\n";
        $output .= $indent . "\t</li>\n";
      }
    }
  }

  function end_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= $indent . "</ul>\n";
  }

  function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
    $indent = ($depth) ? str_repeat("\t", $depth + 1) : '';
    $classes = empty($item->classes) ? array() : (array) $item->classes;
    $classes[] = 'mobile-menu-item-' . $item->ID;

    $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
    $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

    $id = apply_filters('nav_menu_item_id', 'mobile-menu-item-'. $item->ID, $item, $args);
    $id = $id ? ' id="' . esc_attr($id) . '"' : '';

    $has_children = in_array('menu-item-has-children', $classes);

    // Store parent item for submenu generation
    if ($has_children && $depth === 0) {
      $this->parent_item = $item;
    }

    $output .= $indent . '<li' . $id . $class_names .'>';

    $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
    $attributes .= ! empty($item->target) ? ' target="' . esc_attr($item->target) .'"' : '';
    $attributes .= ! empty($item->xfn) ? ' rel="'    . esc_attr($item->xfn) .'"' : '';
    $attributes .= ! empty($item->url) ? ' href="'   . esc_attr($item->url) .'"' : '';

    $item_output = isset($args->before) ? $args->before : '';

    if ($has_children && $depth === 0) {
      // Parent item with children at root level - use submenu-trigger class
      $item_output .= '<a href="#" class="submenu-trigger">';
      $item_output .= apply_filters('the_title', $item->title, $item->ID);
      $item_output .= '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
      $item_output .= '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
      $item_output .= '</svg>';
      $item_output .= '</a>';
    } else {
      // Regular menu item (including submenu items)
      $item_output .= '<a' . $attributes .'>';
      $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
      $item_output .= '</a>';
    }

    $item_output .= isset($args->after) ? $args->after : '';

    $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
  }

  function end_el(&$output, $item, $depth = 0, $args = null) {
    $indent = ($depth) ? str_repeat("\t", $depth + 1) : '';
    $output .= "</li>\n";
  }
}

// Fallback menu functions
function mr9_main_fallback_menu() {
  echo '<nav class="main-navigation">';
  echo '<ul class="main-menu-list">';
  echo '<li><a href="' . esc_url(home_url('/')) . '">Início</a></li>';
  echo '<li><a href="#">Agricultura</a></li>';
  echo '<li><a href="#">Pecuária</a></li>';
  echo '<li><a href="#">Tempo</a></li>';
  echo '<li><a href="#">Cotações</a></li>';
  echo '<li><a href="#">Empreendedorismo</a></li>';
  echo '<li><a href="#">Soja Brasil</a></li>';
  echo '<li><a href="#">Aves e Suínos</a></li>';
  echo '<li><a href="#">Economia</a></li>';
  echo '<li><a href="#">Política</a></li>';
  echo '<li><a href="#">Sustentabilidade</a></li>';
  echo '<li><a href="#">Tecnologia</a></li>';
  echo '<li><a href="#">Mercado</a></li>';
  echo '<li><a href="#">Exportação</a></li>';
  echo '<li><a href="#">Importação</a></li>';
  echo '<li><a href="#">Commodities</a></li>';
  echo '<li><a href="#">Agronegócio</a></li>';
  echo '<li><a href="#">Inovação</a></li>';
  echo '<li><a href="#">Pesquisa</a></li>';
  echo '<li><a href="#">Desenvolvimento</a></li>';
  echo '</ul>';
  echo '</nav>';
}

function mr9_mobile_fallback_menu() {
  echo '<ul class="mobile-menu-list">';
  echo '<li><a href="' . esc_url(home_url('/')) . '">Início</a></li>';

  // Agricultura com submenu
  echo '<li class="menu-item-has-children">';
  echo '<a href="#" class="submenu-trigger">';
  echo 'Agricultura';
  echo '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
  echo '</svg>';
  echo '</a>';
  echo '<ul class="mobile-submenu">';
  echo '<li class="submenu-back">';
  echo '<button class="back-button">';
  echo '<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>';
  echo '</svg>';
  echo 'Voltar';
  echo '</button>';
  echo '</li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/agricultura')) . '" class="submenu-main">Tudo sobre Agricultura</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/milho')) . '">Milho</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/trigo')) . '">Trigo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/laranja')) . '">Laranja</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/hortifruti')) . '">Hortifrúti</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/horta-em-casa')) . '">Horta em Casa</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/feijao')) . '">Feijão</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/algodao')) . '">Algodão</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/soja')) . '">Soja</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/cana')) . '">Cana</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/arroz')) . '">Arroz</a></li>';
  echo '</ul>';
  echo '</li>';

  // Pecuária com submenu
  echo '<li class="menu-item-has-children">';
  echo '<a href="#" class="submenu-trigger">';
  echo 'Pecuária';
  echo '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
  echo '</svg>';
  echo '</a>';
  echo '<ul class="mobile-submenu">';
  echo '<li class="submenu-back">';
  echo '<button class="back-button">';
  echo '<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>';
  echo '</svg>';
  echo 'Voltar';
  echo '</button>';
  echo '</li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/pecuaria')) . '" class="submenu-main">Tudo sobre Pecuária</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/bovinos')) . '">Bovinos</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/suinos')) . '">Suínos</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/aves')) . '">Aves</a></li>';
  echo '</ul>';
  echo '</li>';

  echo '<li><a href="' . esc_url(home_url('/categoria/tempo')) . '">Tempo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/cotacoes')) . '">Cotações</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/empreendedorismo')) . '">Empreendedorismo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/soja-brasil')) . '">Soja Brasil</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/economia')) . '">Economia</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/politica')) . '">Política</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/sustentabilidade')) . '">Sustentabilidade</a></li>';
  echo '<li><a href="' . esc_url(home_url('/tv-ao-vivo')) . '" class="tv-live-link"><span class="live-indicator"></span>TV ao vivo</a></li>';
  echo '</ul>';
}

// Lazy Load Endpoint seguro
add_action('rest_api_init', function() {
  register_rest_route('mr9/v1', '/load-posts', [
    'methods' => 'GET',
    'callback' => function( $request ) {
      $paged = $request->get_param('page') ?: 1;
      $query = new WP_Query([
        'posts_per_page' => 4,
        'paged' => $paged
      ]);
      ob_start();
      if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
          $query->the_post();
          echo '<article class="latest-card">';
          echo '<a href="' . get_permalink() . '">';
          if ( has_post_thumbnail() ) {
            echo '<div class="thumb">' . get_the_post_thumbnail(get_the_ID(), 'medium') . '</div>';
          }
          echo '<div class="info">';
          echo '<h3 class="title">' . get_the_title() . '</h3>';
          echo '<time datetime="' . get_the_date('c') . '">' . get_the_date() . '</time>';
          echo '</div>';
          echo '</a>';
          echo '</article>';
        }
        wp_reset_postdata();
      }
      return ob_get_clean();
    }
  ]);
});


// Registro de menus
function mr9_register_menus() {
    register_nav_menu('primary', __('Primary Menu', 'mr9'));
}
add_action('after_setup_theme', 'mr9_register_menus');

// Post views counter for popular posts
function mr9_set_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    }else{
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}

// Remove prefetch to avoid false counts
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);

// Track post views on single posts
function mr9_track_post_views ($post_id) {
    if ( !is_single() ) return;
    if ( empty ( $post_id) ) {
        global $post;
        $post_id = $post->ID;
    }
    mr9_set_post_views($post_id);
}
add_action( 'wp_head', 'mr9_track_post_views');

// Get post views count
function mr9_get_post_views($postID){
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0";
    }
    return $count;
}

// Generate intelligent post summary with caching
function generate_intelligent_summary($content, $max_paragraphs = 4) {
    if (empty($content)) {
        return '';
    }

    // Check cache first
    $post_id = get_the_ID();
    $cache_key = 'intelligent_summary_' . $post_id . '_' . md5($content);
    $cached_summary = get_transient($cache_key);

    if ($cached_summary !== false) {
        return $cached_summary;
    }

    // Extract only paragraph content, avoiding titles and lists
    $paragraphs = array();

    // Split content by HTML paragraphs first
    if (strpos($content, '<p>') !== false) {
        preg_match_all('/<p[^>]*>(.*?)<\/p>/s', $content, $matches);
        $paragraph_contents = $matches[1];
    } else {
        // If no HTML paragraphs, split by double line breaks
        $paragraph_contents = explode("\n\n", $content);
    }

    // Clean and filter paragraphs
    foreach ($paragraph_contents as $paragraph) {
        $clean_paragraph = wp_strip_all_tags($paragraph);
        $clean_paragraph = preg_replace('/\s+/', ' ', $clean_paragraph);
        $clean_paragraph = trim($clean_paragraph);

        // Skip if too short, or looks like a title/heading
        if (strlen($clean_paragraph) < 50) continue;

        // Skip if it looks like a title (all caps, very short, etc.)
        if (strlen($clean_paragraph) < 100 && (
            strtoupper($clean_paragraph) === $clean_paragraph || // All caps
            preg_match('/^[A-Z][^.!?]*$/', $clean_paragraph) || // No punctuation
            str_word_count($clean_paragraph) < 8 // Too few words
        )) {
            continue;
        }

        // Skip if it looks like a list item
        if (preg_match('/^[\s]*[-•*]\s/', $clean_paragraph) ||
            preg_match('/^\d+[\.\)]\s/', $clean_paragraph)) {
            continue;
        }

        // Skip if it's mostly numbers or dates
        if (preg_match('/^\d+[\/\-\.\s\d]*$/', trim($clean_paragraph))) {
            continue;
        }

        $paragraphs[] = $clean_paragraph;
    }

    if (empty($paragraphs)) {
        return '';
    }

    // If content is too short, return first paragraph
    $total_content = implode(' ', $paragraphs);
    if (strlen($total_content) < 300) {
        return '<p>' . $paragraphs[0] . '</p>';
    }

    // Split paragraphs into sentences
    $all_sentences = array();
    foreach ($paragraphs as $paragraph_index => $paragraph) {
        $sentences = preg_split('/[.!?]+/', $paragraph, -1, PREG_SPLIT_NO_EMPTY);
        $sentences = array_map('trim', $sentences);
        $sentences = array_filter($sentences, function($sentence) {
            return strlen($sentence) > 30; // Filter out very short sentences
        });

        // Add paragraph context to each sentence
        foreach ($sentences as $sentence) {
            $all_sentences[] = array(
                'text' => $sentence,
                'paragraph_index' => $paragraph_index,
                'original_paragraph' => $paragraph
            );
        }
    }

    if (empty($all_sentences)) {
        return '';
    }

    // Score sentences based on importance
    $scored_sentences = array();
    $total_sentences = count($all_sentences);

    foreach ($all_sentences as $index => $sentence_data) {
        $sentence = $sentence_data['text'];
        $paragraph_index = $sentence_data['paragraph_index'];
        $score = 0;

        // Position scoring within paragraphs - first paragraphs are more important
        if ($paragraph_index < 2) {
            $score += 3; // First paragraphs
        } elseif ($paragraph_index > count($paragraphs) - 3) {
            $score += 2; // Last paragraphs
        }

        // Position within sentence list
        if ($index < 3) {
            $score += 2; // First sentences overall
        }

        // Length scoring - medium length sentences are often better
        $length = strlen($sentence);
        if ($length > 60 && $length < 180) {
            $score += 2;
        }

        // Enhanced keyword scoring with categories
        $keyword_categories = array(
            'high_importance' => array(
                'anunciou', 'revelou', 'confirmou', 'decidiu', 'aprovou',
                'lançou', 'implementou', 'estabeleceu', 'determinou'
            ),
            'results' => array(
                'resultado', 'conclusão', 'descoberta', 'achado', 'evidência',
                'demonstrou', 'provou', 'indicou', 'mostrou', 'revelou'
            ),
            'data_numbers' => array(
                'dados', 'estatística', 'número', 'percentual', 'milhão',
                'bilhão', 'crescimento', 'aumento', 'redução', 'queda'
            ),
            'authorities' => array(
                'ministério', 'governo', 'presidente', 'ministro',
                'secretário', 'diretor', 'especialista', 'pesquisador'
            ),
            'business' => array(
                'empresa', 'mercado', 'economia', 'negócio', 'setor',
                'indústria', 'produção', 'vendas', 'lucro', 'investimento'
            ),
            'agriculture' => array(
                'safra', 'plantio', 'colheita', 'produção', 'agricultura',
                'pecuária', 'soja', 'milho', 'algodão', 'cana', 'café'
            )
        );

        // Score based on keyword categories
        foreach ($keyword_categories as $category => $words) {
            foreach ($words as $word) {
                if (stripos($sentence, $word) !== false) {
                    switch ($category) {
                        case 'high_importance':
                            $score += 3;
                            break;
                        case 'results':
                        case 'data_numbers':
                            $score += 2;
                            break;
                        default:
                            $score += 1;
                            break;
                    }
                }
            }
        }

        // Bonus for sentences with numbers/percentages
        if (preg_match('/\d+[%]?/', $sentence)) {
            $score += 2;
        }

        // Bonus for quotes or statements
        if (preg_match('/"[^"]*"/', $sentence) || stripos($sentence, 'disse') !== false) {
            $score += 1;
        }

        // Avoid repetitive or generic sentences
        $generic_phrases = array(
            'como já mencionado', 'conforme dito', 'vale lembrar',
            'é importante destacar que', 'cabe ressaltar',
            'neste contexto', 'neste sentido', 'vale destacar'
        );

        foreach ($generic_phrases as $phrase) {
            if (stripos($sentence, $phrase) !== false) {
                $score -= 2;
            }
        }

        $scored_sentences[] = array(
            'sentence' => $sentence,
            'score' => $score,
            'index' => $index,
            'paragraph_index' => $paragraph_index,
            'original_paragraph' => $sentence_data['original_paragraph']
        );
    }

    // Sort by score (descending) and then by original position
    usort($scored_sentences, function($a, $b) {
        if ($a['score'] == $b['score']) {
            return $a['index'] - $b['index'];
        }
        return $b['score'] - $a['score'];
    });

    // Select top sentences for summary
    $selected_sentences = array();
    $total_length = 0;
    $max_length = 800; // Maximum summary length

    foreach ($scored_sentences as $item) {
        if (count($selected_sentences) >= $max_paragraphs * 2) {
            break;
        }

        $sentence_length = strlen($item['sentence']);
        if ($total_length + $sentence_length <= $max_length) {
            $selected_sentences[] = $item;
            $total_length += $sentence_length;
        }
    }

    // Sort selected sentences by original order
    usort($selected_sentences, function($a, $b) {
        return $a['index'] - $b['index'];
    });

    // Build summary paragraphs avoiding mixing different source paragraphs
    $summary_text = '';
    $used_paragraphs = array();
    $summary_paragraphs = array();

    foreach ($selected_sentences as $sentence_data) {
        $sentence = trim($sentence_data['sentence']);
        $paragraph_index = $sentence_data['paragraph_index'];
        $original_paragraph = $sentence_data['original_paragraph'];

        // Skip if we already used this source paragraph
        if (in_array($paragraph_index, $used_paragraphs)) {
            continue;
        }

        // Clean and format sentence
        $sentence = ucfirst($sentence); // Capitalize first letter

        // Add period if missing
        if (!preg_match('/[.!?]$/', $sentence)) {
            $sentence .= '.';
        }

        // Use the sentence as a complete paragraph to avoid mixing contexts
        $summary_paragraphs[] = $sentence;
        $used_paragraphs[] = $paragraph_index;

        // Limit to max paragraphs
        if (count($summary_paragraphs) >= $max_paragraphs) {
            break;
        }
    }

    // If we don't have enough paragraphs, try to add more sentences from unused paragraphs
    if (count($summary_paragraphs) < $max_paragraphs) {
        foreach ($selected_sentences as $sentence_data) {
            if (count($summary_paragraphs) >= $max_paragraphs) {
                break;
            }

            $paragraph_index = $sentence_data['paragraph_index'];

            // Skip if we already used this source paragraph
            if (in_array($paragraph_index, $used_paragraphs)) {
                continue;
            }

            $sentence = trim($sentence_data['sentence']);
            $sentence = ucfirst($sentence);

            if (!preg_match('/[.!?]$/', $sentence)) {
                $sentence .= '.';
            }

            $summary_paragraphs[] = $sentence;
            $used_paragraphs[] = $paragraph_index;
        }
    }

    // Convert paragraphs to HTML
    foreach ($summary_paragraphs as $paragraph) {
        $summary_text .= '<p>' . trim($paragraph) . '</p>';
    }

    // Cache the result for 24 hours
    set_transient($cache_key, $summary_text, 24 * HOUR_IN_SECONDS);

    return $summary_text;
}

// Clear summary cache when post is updated
function clear_summary_cache($post_id) {
    if (wp_is_post_revision($post_id)) {
        return;
    }

    // Delete all cached summaries for this post
    global $wpdb;
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
        '_transient_intelligent_summary_' . $post_id . '_%'
    ));
}
add_action('save_post', 'clear_summary_cache');

// Debug function for summary generation (only for admins)
function debug_summary_generation($post_id = null) {
    if (!current_user_can('manage_options')) {
        return;
    }

    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $post = get_post($post_id);
    if (!$post) {
        return;
    }

    $content = $post->post_content;
    $summary = generate_intelligent_summary($content, 4);

    echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border-left: 4px solid #007cba;">';
    echo '<h4>Debug: Resumo Inteligente</h4>';
    echo '<p><strong>Post ID:</strong> ' . $post_id . '</p>';
    echo '<p><strong>Tamanho do conteúdo:</strong> ' . strlen($content) . ' caracteres</p>';
    echo '<p><strong>Tamanho do resumo:</strong> ' . strlen(wp_strip_all_tags($summary)) . ' caracteres</p>';
    echo '<p><strong>Número de parágrafos:</strong> ' . substr_count($summary, '<p>') . '</p>';
    echo '<div><strong>Resumo gerado:</strong></div>';
    echo '<div style="background: white; padding: 15px; margin-top: 10px; border-radius: 4px;">';
    echo $summary;
    echo '</div>';
    echo '</div>';
}

// Add summary quality metrics
function get_summary_quality_score($original_content, $summary) {
    $original_length = strlen(wp_strip_all_tags($original_content));
    $summary_length = strlen(wp_strip_all_tags($summary));

    if ($original_length == 0) {
        return 0;
    }

    $compression_ratio = $summary_length / $original_length;
    $paragraph_count = substr_count($summary, '<p>');

    $quality_score = 0;

    // Ideal compression ratio: 10-20%
    if ($compression_ratio >= 0.1 && $compression_ratio <= 0.2) {
        $quality_score += 40;
    } elseif ($compression_ratio >= 0.05 && $compression_ratio <= 0.3) {
        $quality_score += 20;
    }

    // Ideal paragraph count: 3-4
    if ($paragraph_count >= 3 && $paragraph_count <= 4) {
        $quality_score += 30;
    } elseif ($paragraph_count >= 2 && $paragraph_count <= 5) {
        $quality_score += 15;
    }

    // Summary length should be reasonable (200-800 chars)
    if ($summary_length >= 200 && $summary_length <= 800) {
        $quality_score += 30;
    } elseif ($summary_length >= 100 && $summary_length <= 1000) {
        $quality_score += 15;
    }

    return min(100, $quality_score);
}
