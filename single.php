<?php get_header(); ?>

<!-- Breadcrumb -->
<div class="container">
  <nav class="breadcrumb-nav">
    <a href="<?php echo esc_url(home_url('/')); ?>">Home</a>
    <?php
    $categories = get_the_category();
    if ( ! empty( $categories ) ) : ?>
      <span class="breadcrumb-separator">></span>
      <a href="<?php echo esc_url( get_category_link( $categories[0]->term_id ) ); ?>">
        <?php echo esc_html( $categories[0]->name ); ?>
      </a>
    <?php endif; ?>
  </nav>
</div>

<?php while ( have_posts() ) : the_post(); ?>

<!-- Post Header - Full Width -->
<div class="container">
  <div class="single-post-header">
    <?php
    $categories = get_the_category();
    if ( ! empty( $categories ) ) : ?>
      <div class="post-category-header">
        <a href="<?php echo esc_url( get_category_link( $categories[0]->term_id ) ); ?>" class="category-link-header">
          <?php echo esc_html( $categories[0]->name ); ?>
        </a>
      </div>
    <?php endif; ?>

    <h1 class="single-title-header"><?php the_title(); ?></h1>

    <?php if ( has_excerpt() ) : ?>
      <div class="single-subtitle-header">
        <?php the_excerpt(); ?>
      </div>
    <?php endif; ?>

    <div class="post-meta-header">
      <div class="author-info-header">
        <?php echo get_avatar( get_the_author_meta( 'ID' ), 40, '', '', array( 'class' => 'author-avatar-header' ) ); ?>
        <div class="author-details-header">
          <span class="author-name-header"><?php the_author(); ?></span>
          <span class="post-date-header"><?php echo get_the_date('d/m/Y H:i'); ?></span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Layout with Grid -->
<main class="container single-post-layout">

  <!-- Social Share Sidebar (Sticky) -->
  <aside class="social-share-sidebar">
    <div class="social-share-sticky">
      <a href="https://wa.me/?text=<?php echo urlencode(get_the_title() . ' - ' . get_permalink()); ?>"
         target="_blank"
         class="social-btn whatsapp"
         aria-label="Compartilhar no WhatsApp">
        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.386"/>
        </svg>
      </a>

      <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>"
         target="_blank"
         class="social-btn facebook"
         aria-label="Compartilhar no Facebook">
        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      </a>

      <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode(get_the_title()); ?>&url=<?php echo urlencode(get_permalink()); ?>"
         target="_blank"
         class="social-btn twitter"
         aria-label="Compartilhar no Twitter">
        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
        </svg>
      </a>

      <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>"
         target="_blank"
         class="social-btn linkedin"
         aria-label="Compartilhar no LinkedIn">
        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
        </svg>
      </a>
    </div>
  </aside>

  <!-- Main Content -->
  <div class="single-main-content">
    <article <?php post_class('single-article'); ?>>

      <?php if ( has_post_thumbnail() ) : ?>
        <div class="single-featured-image">
          <?php the_post_thumbnail('large', array('class' => 'featured-img')); ?>
        </div>
      <?php endif; ?>

      <!-- Post Summary Component -->
      <?php
      $post_excerpt = get_the_excerpt();
      if (!empty($post_excerpt)) : ?>
        <div class="post-summary-component">
          <div class="summary-header" onclick="toggleSummary()">
            <h3 class="summary-title">Ver resumo</h3>
            <svg class="summary-arrow" width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="summary-content" id="summaryContent">
            <div class="summary-text">
              <?php echo wp_kses_post($post_excerpt); ?>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <div class="single-content">
        <?php
        // Display post content
        the_content();
        ?>
      </div>

      <!-- Author Box -->
      <div class="author-box">
        <div class="author-box-content">
          <div class="author-box-avatar">
            <?php echo get_avatar(get_the_author_meta('ID'), 80); ?>
          </div>
          <div class="author-box-info">
            <h3 class="author-box-name"><?php the_author(); ?></h3>
            <p class="author-box-description">
              <?php
              $author_description = get_the_author_meta('description');
              echo $author_description ? esc_html($author_description) : 'Jornalista e redator especializado em conteúdo editorial.';
              ?>
            </p>
          </div>
        </div>
      </div>



      <footer class="single-footer">
        <div class="post-navigation">
          <div class="nav-previous"><?php previous_post_link('%link', '← Post anterior'); ?></div>
          <div class="nav-next"><?php next_post_link('%link', 'Próximo post →'); ?></div>
        </div>
      </footer>

    </article>
  </div>

    <!-- Sidebar -->
    <aside class="single-sidebar">
      <div class="sidebar-content">
        <!-- Table of Contents -->
        <div class="table-of-contents-widget">
          <h3 class="toc-title">Índice</h3>
          <div id="table-of-contents" class="toc-list">
            <!-- TOC will be generated by JavaScript -->
          </div>
        </div>

        <h3 class="sidebar-title">Mais lidas</h3>
        <div class="popular-posts">
          <?php
          // Try to get popular posts first
          $popular_posts = new WP_Query(array(
            'posts_per_page' => 5,
            'meta_key' => 'post_views_count',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
            'post__not_in' => array(get_the_ID()),
            'meta_query' => array(
              array(
                'key' => 'post_views_count',
                'value' => '0',
                'compare' => '>'
              )
            )
          ));

          // If no popular posts, get recent posts
          if (!$popular_posts->have_posts()) {
            $popular_posts = new WP_Query(array(
              'posts_per_page' => 5,
              'orderby' => 'date',
              'order' => 'DESC',
              'post__not_in' => array(get_the_ID())
            ));
          }

          if ($popular_posts->have_posts()) :
            $post_count = 0;
            while ($popular_posts->have_posts()) : $popular_posts->the_post();
              $post_count++; ?>
              <article class="popular-post-item">
                <a href="<?php the_permalink(); ?>" class="popular-post-link">
                  <?php if (has_post_thumbnail()) : ?>
                    <div class="popular-post-thumb">
                      <?php the_post_thumbnail('thumbnail'); ?>
                    </div>
                  <?php endif; ?>
                  <div class="popular-post-content">
                    <?php
                    $categories = get_the_category();
                    if (!empty($categories)) : ?>
                      <span class="popular-post-category"><?php echo esc_html($categories[0]->name); ?></span>
                    <?php endif; ?>
                    <h4 class="popular-post-title"><?php the_title(); ?></h4>
                  </div>
                </a>
              </article>
            <?php endwhile;
            wp_reset_postdata();
          else : ?>
            <p style="text-align: center; color: #666; font-style: italic;">Nenhum post encontrado.</p>
          <?php endif;
          ?>
        </div>
      </div>
    </aside>

  <?php endwhile; ?>
</main>

<!-- See Also Section - Full Width -->
<div class="see-also-section">
  <div class="see-also-container">

    <div class="see-also-content">
      <h3 class="see-also-title">Veja também</h3>
      <div class="see-also-list">
        <?php
        // Get related posts for "See Also" section
        $see_also_posts = new WP_Query(array(
          'posts_per_page' => 5,
          'post__not_in' => array(get_the_ID()),
          'category__in' => wp_get_post_categories(get_the_ID()),
          'orderby' => 'date',
          'order' => 'DESC'
        ));

        if ($see_also_posts->have_posts()) :
          while ($see_also_posts->have_posts()) : $see_also_posts->the_post(); ?>
            <article class="see-also-item">
              <a href="<?php the_permalink(); ?>" class="see-also-link" aria-label="Leia mais: <?php echo esc_attr(get_the_title()); ?>">
                <?php if (has_post_thumbnail()) : ?>
                  <div class="see-also-image">
                    <?php the_post_thumbnail('medium'); ?>
                  </div>
                <?php endif; ?>
                <div class="see-also-content">
                  <?php
                  $categories = get_the_category();
                  if (!empty($categories)) : ?>
                    <span class="see-also-category"><?php echo esc_html($categories[0]->name); ?></span>
                  <?php endif; ?>
                  <h4 class="see-also-post-title">
                    <?php the_title(); ?>
                  </h4>
                </div>
              </a>
            </article>
          <?php endwhile;
          wp_reset_postdata();
        else : ?>
          <p style="text-align: center; color: #666; font-style: italic;">Nenhum post relacionado encontrado.</p>
        <?php endif; ?>
      </div>
    </div>

    <div class="see-also-sidebar">
      <div class="see-also-ad">
        <p>Espaço para publicidade</p>
      </div>
    </div>
  </div>
</div>

<?php get_footer(); ?>
